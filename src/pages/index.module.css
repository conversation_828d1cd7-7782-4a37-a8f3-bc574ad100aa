/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

:root {
  --primary: #4A6FFF;
  --primary-dark: #3A5BDF;
  --secondary: #FF6B6B;
  --dark: #333333;
  --light: #FFFFFF;
  --gray: #F5F7FA;
  --text: #333333;
  --text-light: #555555;
}

/* Main Navigation */
.mainNav {
  padding: 0.5rem 0;
  background-color: var(--light);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  text-decoration: none;
}

.navLinks {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navLinks a {
  color: var(--text);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.navLinks a:hover {
  color: var(--primary);
}

.languageSelector {
  background-color: transparent;
  border: 0px;
  border-radius: 5px;
  cursor: pointer;
}

/* Hero Section */
.hero {
  padding: 5rem 0;
  /* background: linear-gradient(135deg, #f5f7ff 0%, #e0e6ff 100%); */
  position: relative;
  overflow: hidden;
}

.heroContent {
  max-width: 650px;
  z-index: 10;
  position: relative;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: var(--text-light);
}

.heroButtons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.heroImage {
  position: absolute;
  right: -5%;
  top: 50%;
  transform: translateY(-50%);
  width: 55%;
  max-width: 700px;
  border-radius: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: var(--primary);
  color: white;
  border-radius: 6px;
  font-weight: 600;
  text-decoration: none;
  transition: background-color 0.3s;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn:hover {
  background-color: var(--primary-dark);
}

.btnSecondary {
  background-color: var(--light);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btnSecondary:hover {
  background-color: var(--primary);
  color: white;
}

.btnSm {
  padding: 0.4rem 2rem;
}

/* Beyond ChatGPT Section */
.beyondChatgpt {
  padding: 6rem 0;
  background-color: rgb(207, 238, 235);
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.sectionDescription {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
  font-size: 1.3rem;
  color: var(--text-light);
}

.twoColumnGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.benefitsContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.benefitCard {
  padding: 2rem;
  padding-bottom: 1rem;
  background-color: var(--light);
  border-radius: 10px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.benefitCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.cardTitle {
  font-size: 1.8rem;
  font-weight: bold;
  color: #3498db;
  text-decoration: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  column-gap: 1rem;
  margin-bottom: 1rem;
}

.cardTitle h4 {
  margin: 0;
  font-size: 1.4rem;
}

.cardTitle a {
  color: #3498db;
  /* text-decoration: none; */
}

.benefitIcon {
  font-size: 2.5rem;
  color: var(--primary);
}

.toolsSection {
  padding: 6rem 0;
  background-color: rgb(248, 245, 234);
}

/* Feature Sections */
.featureSection {
  padding: 6rem 0;
  background-color: var(--gray);
}

.featureSection:nth-child(even) {
  background-color: var(--light);
}

.featureGrid {
  display: block;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.featureContent {
  max-width: 500px;
  margin-top: 1.5rem;
}

.featureContent h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.featureContent p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  color: var(--text-light);
}

.featureList {
  list-style-type: none;
  margin-top: 1.0rem;
  padding-left: 0;
}

.featureList li {
  margin-bottom: 0.7rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  line-height: 1.4;
  font-size: 1.1rem;
  color: var(--text-light);
}

.featureList li:before {
  content: "•";
  color: var(--primary);
  font-weight: bold;
  font-size: 1.5rem;
  line-height: 1;
}

.featureImage {
  width: 100%;
  padding: 6px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;
}

.featureImage:hover {
  transform: scale(1.02);
}

.resourceCard {
  transition: transform 0.3s, box-shadow 0.3s;
}

.resourceCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.resourceLink {
  position: relative;
  display: inline-block;
}

.resourceLink:after {
  content: '';
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: var(--primary);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.resourceLink:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* 思维方法容器和项目样式 */
.thinkingMethodsContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 30px;
  background-color: rgba(255,255,255,0.7);
  padding: 10px 20px;
  border-radius: 8px;
  max-width: 90%;
}

.thinkingMethodItem {
  display: flex;
  align-items: center;
  margin: 10px 20px;
}

.thinkingMethodIcon {
  font-size: 24px;
  margin-right: 10px;
}

.thinkingMethodText {
  font-weight: bold;
}

/* 资源网格 */
.resourcesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .thinkingMethodsContainer {
    flex-direction: column;
    align-items: flex-start;
    padding: 15px;
  }

  .thinkingMethodItem {
    margin: 8px 0;
  }

  .heroButtons {
    flex-direction: column;
    align-items: center;
  }

  .heroButtons a {
    margin: 10px 0 !important;
    width: 100%;
    text-align: center;
  }

  .resourcesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* 标题和副标题在移动端的样式 */
  .hero h1 {
    font-size: 2rem !important;
  }

  .heroSubtitle {
    font-size: 1.1rem !important;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
  }
}

.docsFeatureImage {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  background-color: #f8f8f3;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;
}

.docsFeatureImage:hover {
  transform: scale(1.02);
}

.fullWidthImage {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  display: block;
  border-radius: 10px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}
/* Multi-Model Section */
.multiModelAdvantage {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modelLogosContainer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 3rem;
  margin-bottom: 2rem;
  background-color: #ebedf3;
  width: fit-content;
  padding: 2rem;
  border-radius: 10px;
}

.modelLogoItem {
  text-align: center;
}

.modelLogo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-bottom: 0.5rem;
}

.modelName {
  font-weight: 500;
  margin: 0;
}

.advantageText {
  text-align: center;
  font-size: 1.1rem;
  color: var(--text-light);
}

/* Use Cases Section */
.useCases {
  padding: 6rem 0;
  background-color: rgb(248, 244, 230);
}

.useCasesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 3rem;
}

.useCaseCard {
  background-color: var(--light);
  padding: 1rem;
  padding-bottom: 0.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  border-radius: 10px;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
}

.useCaseCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.useCaseIcon {
  color: var(--primary);
  font-size: 1.5rem;
}

/* Workspace Section */
.workspaceSection {
  padding: 6rem 0;
  background-color: aliceblue;

}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.ctaBtn {
  background-color: white;
  color: var(--primary);
}

.ctaBtn:hover {
  background-color: var(--gray);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .heroImage {
    width: 45%;
    right: 0;
  }
}

@media (max-width: 768px) {
  .toolsList {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .hero h1 {
    font-size: 2.8rem;
  }

  .featureContent h2,
  .sectionTitle {
    font-size: 2rem;
  }

  .hero {
    padding: 5rem 0 10rem;
  }

  .heroImage {
    position: absolute;
    right: 50%;
    top: auto;
    bottom: -6rem;
    transform: translateX(50%);
    width: 80%;
  }

  .featureGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
    flex-direction: column !important;
  }

  .featureContent {
    max-width: 100%;
    margin-top: 1.5rem;
  }

  .benefitsContainer, .twoColumnGrid, .useCasesGrid {
    grid-template-columns: 1fr;
  }

  /* 确保在移动端图片和内容都是全宽 */
  .featureGrid > div {
    flex: 1 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 2.3rem;
  }

  .navLinks {
    display: none;
  }

  .hero {
    padding: 4rem 0 10rem;
  }
}

.pageSection {
  padding: 40px 0;
  min-height: calc(100vh - 3.5rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Slides Page Styles */
.slidesHeader {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f5f7ff 0%, #e0e6ff 100%);
  position: relative;
  min-height: calc(100vh - 3.5rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.slidesContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.slidesTitle {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
}

.slidesSubtitle {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  color: var(--text-light);
}

.slidesTarget {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: var(--text-light);
  text-align: center;
}

.slidesFeatureSection {
  padding: 6rem 0;
  background-color: #F0FFF0;
}

.slidesAISection {
  padding: 6rem 0;
  background-color: rgb(255, 239, 255);
}


.slidesFeatureIcon {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.slidesCardContent {
  background-color: var(--light);
  padding: 2rem;
  border-radius: 10px;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
}

.slidesCardContent:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.slidesRow {
  display: flex;
  flex-wrap: wrap;
  margin: 2rem 0;
}

.slidesCol4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 1rem;
}

.slidesCol8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
  padding: 0 1rem;
}

@media (max-width: 768px) {
  .slidesCol4, .slidesCol8 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .featureGrid {
    display: flex;
    align-items: center;
  }

  .featureContent {
    flex: 2;
    margin-top: 0;
  }

  /* 图片在左侧的布局 */
  .imageLeft {
    flex-direction: row;
  }

  /* 图片在右侧的布局 */
  .imageRight {
    flex-direction: row-reverse;
  }
}

.centerContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 2rem 0;
}

.order1 {
  order: 1;
}

.order2 {
  order: 2;
}

/* HeroSection responsive styles */
@media (max-width: 768px) {
  .hero .row {
    flex-direction: column-reverse;
  }

  .hero h1 {
    font-size: 2.5rem !important;
    text-align: center;
  }

  .hero p {
    text-align: center;
    font-size: 1.1rem !important;
  }

  .hero .col-lg-6:first-child {
    text-align: center;
    margin-top: 2rem;
  }

  .hero .col-lg-6:last-child {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .hero {
    padding: 80px 0 60px !important;
  }

  .hero h1 {
    font-size: 2rem !important;
  }

  .hero p {
    font-size: 1rem !important;
  }

  .hero .button {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .hero div[style*="display: flex"][style*="gap: 1rem"] {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .hero div[style*="display: flex"][style*="gap: 2rem"] {
    flex-direction: column;
    gap: 1rem !important;
    justify-content: center;
  }
}