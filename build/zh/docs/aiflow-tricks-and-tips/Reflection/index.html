<!doctype html>
<html lang="zh" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Reflection" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Optimizing AI Output | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Reflection"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Optimizing AI Output | FunBlocks AI"><meta data-rh="true" name="description" content="Innovative Approaches for AI Self-Reflection and Enhancement"><meta data-rh="true" property="og:description" content="Innovative Approaches for AI Self-Reflection and Enhancement"><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Reflection"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Reflection" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Reflection" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Reflection" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.6c96a670.css">
<script src="/zh/assets/js/runtime~main.10288118.js" defer="defer"></script>
<script src="/zh/assets/js/main.61f1b693.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/ai-extensions">AI Extensions</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Reflection" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Reflection" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="回到顶部" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="文档侧边栏" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/zh/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="展开侧边栏分类 &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/zh/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="折叠侧边栏分类 &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/ai-tools">AI Tools</a><button aria-label="展开侧边栏分类 &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="页面路径"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="主页面" class="breadcrumbs__link" href="/zh/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/zh/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Optimizing AI Output</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">本页总览</button></div><div class="theme-doc-markdown markdown"><header><h1>Optimizing AI Output</h1></header>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="innovative-approaches-for-ai-self-reflection-and-enhancement"><em>Innovative Approaches for AI Self-Reflection and Enhancement</em><a href="#innovative-approaches-for-ai-self-reflection-and-enhancement" class="hash-link" aria-label="innovative-approaches-for-ai-self-reflection-and-enhancement的直接链接" title="innovative-approaches-for-ai-self-reflection-and-enhancement的直接链接">​</a></h2>
<p>Not satisfied with your AI-generated results? FunBlocks AIFlow offers several powerful methods to refine and enhance the content you receive. In this installment of our series, we&#x27;ll explore how to optimize AI outputs through reflection techniques, a key advantage built directly into the AIFlow platform.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="three-ways-to-improve-ai-generated-content">Three Ways to Improve AI-Generated Content<a href="#three-ways-to-improve-ai-generated-content" class="hash-link" aria-label="Three Ways to Improve AI-Generated Content的直接链接" title="Three Ways to Improve AI-Generated Content的直接链接">​</a></h2>
<p>When working with AI-generated content in AIFlow, you have three main options to enhance the results:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-direct-editing">1. Direct Editing<a href="#1-direct-editing" class="hash-link" aria-label="1. Direct Editing的直接链接" title="1. Direct Editing的直接链接">​</a></h3>
<p>You can manually edit any generated subtopic. For example, in mind maps created during brainstorming sessions, you can easily add, delete, or modify subtopics to better align with your needs.</p>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow edit node item" src="/zh/assets/images/aiflow_edit_node_item-e7bdf29d81b65e2f00a55f958bef6256.png" width="2060" height="1374" class="img_ev3q"></p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-model-switching">2. Model Switching<a href="#2-model-switching" class="hash-link" aria-label="2. Model Switching的直接链接" title="2. Model Switching的直接链接">​</a></h3>
<p>In AIFlow, you can choose different large language models (LLMs) to regenerate any generated node, creating a new sibling node. This gives you the flexibility to try various AI approaches to find the output that best serves your purpose.</p>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow support regenerate with new LLM" src="/zh/assets/images/aiflow_regenerate_with_new_llm-547261cfbfcaac0544b94b6cc93464ac.png" width="1534" height="968" class="img_ev3q"></p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-ai-self-reflection">3. AI Self-Reflection<a href="#3-ai-self-reflection" class="hash-link" aria-label="3. AI Self-Reflection的直接链接" title="3. AI Self-Reflection的直接链接">​</a></h3>
<p>Perhaps the most powerful optimization technique is enabling AI self-reflection. With this feature, you can:</p>
<ul>
<li>Have the AI analyze its own output</li>
<li>Generate specific improvement suggestions</li>
<li>Review these suggested optimizations</li>
<li>Add your own adjustments and opinions</li>
<li>Instruct the AI to regenerate content based on this refined optimization plan</li>
</ul>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow AI self reflection and improvement to generated content" src="/zh/assets/images/aiflow_reflection_improve-160f9560e80e121ccbec385e955bbf11.png" width="1938" height="1824" class="img_ev3q"></p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="the-power-of-ai-self-reflection">The Power of AI Self-Reflection<a href="#the-power-of-ai-self-reflection" class="hash-link" aria-label="The Power of AI Self-Reflection的直接链接" title="The Power of AI Self-Reflection的直接链接">​</a></h2>
<p>Having an AI reflect on its own output before regenerating content is a highly effective strategy for improving quality. This approach represents a universal best practice when collaborating with AI systems.</p>
<p>What makes AIFlow special is how this advanced strategy is built directly into the product interface. Instead of writing complex prompts yourself, AIFlow lets you trigger the reflection-optimization cycle with a single click.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="why-this-matters">Why This Matters<a href="#why-this-matters" class="hash-link" aria-label="Why This Matters的直接链接" title="Why This Matters的直接链接">​</a></h2>
<p>The reflection process creates a feedback loop that helps the AI understand your specific needs better. By reviewing the AI&#x27;s self-assessment and adding your own input, you create a collaborative environment where each iteration produces increasingly refined results.</p>
<p>This approach saves time, reduces frustration, and ultimately delivers higher-quality outputs tailored to your exact requirements.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="getting-started-with-reflection">Getting Started With Reflection<a href="#getting-started-with-reflection" class="hash-link" aria-label="Getting Started With Reflection的直接链接" title="Getting Started With Reflection的直接链接">​</a></h2>
<p>To use the reflection feature in AIFlow:</p>
<ol>
<li>Generate initial content</li>
<li>If unsatisfied, click the reflection option</li>
<li>Review the AI&#x27;s self-analysis</li>
<li>Add your own feedback if needed</li>
<li>Approve the optimization plan</li>
<li>Receive improved content based on this reflection</li>
</ol>
<p>Try implementing this reflection strategy in your next AIFlow project to experience how it can transform ordinary AI outputs into exceptional content.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="文件选项卡"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action"><div class="pagination-nav__sublabel">上一页</div><div class="pagination-nav__label">From Ideas to Action</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/docs/aiflow-tricks-and-tips/Notes"><div class="pagination-nav__sublabel">下一页</div><div class="pagination-nav__label">Maximizing Sticky Notes</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#innovative-approaches-for-ai-self-reflection-and-enhancement" class="table-of-contents__link toc-highlight"><em>Innovative Approaches for AI Self-Reflection and Enhancement</em></a></li><li><a href="#three-ways-to-improve-ai-generated-content" class="table-of-contents__link toc-highlight">Three Ways to Improve AI-Generated Content</a><ul><li><a href="#1-direct-editing" class="table-of-contents__link toc-highlight">1. Direct Editing</a></li><li><a href="#2-model-switching" class="table-of-contents__link toc-highlight">2. Model Switching</a></li><li><a href="#3-ai-self-reflection" class="table-of-contents__link toc-highlight">3. AI Self-Reflection</a></li></ul></li><li><a href="#the-power-of-ai-self-reflection" class="table-of-contents__link toc-highlight">The Power of AI Self-Reflection</a></li><li><a href="#why-this-matters" class="table-of-contents__link toc-highlight">Why This Matters</a></li><li><a href="#getting-started-with-reflection" class="table-of-contents__link toc-highlight">Getting Started With Reflection</a></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>